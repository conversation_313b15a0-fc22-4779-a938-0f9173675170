package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import cn.hutool.core.util.StrUtil;
import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springblade.modules.hzyc.DocumentGeneration.service.ICaseInfoService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import cn.hutool.json.JSONArray;

/**
 * 询问通知书文档生成实现类
 *
 * <AUTHOR>
 */
@Service("inquiryNoticeDocument")
public class InquiryNoticeDocumentImpl implements DocumentGenerator {

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @Autowired
    private IDocumentService documentService;
    @Autowired
    private ICaseInfoService icaseInfoService;

    @Override
    public List<Map<String, Object>> processData(Map<String, Object> rawData, String caseId, String mode, String fileId) {
        Map<String, Object> processedData = new HashMap<>(rawData);
        // 如果mode为update，从数据库读取已保存的数据
        if ("update".equals(mode) && caseId != null) {
            DocumentEntity existingDoc = documentService.getById(fileId);
            if (existingDoc != null && existingDoc.getDocumentContent() != null) {
                // 从JSON字符串解析为Map
                processedData = JsonUtil.readMap(existingDoc.getDocumentContent());
                return List.of(processedData);
            }
        }

        // 如果没有找到已保存的数据或mode不是update，根据环境判断数据类型
        System.out.println(caseId);
        // dev环境使用模拟数据(type=0)，prod环境使用真实数据(type=1)
        int dataType = "prod".equals(activeProfile) ? 1 : 0;
        processedData = getMockData(dataType, caseId);
        return List.of(processedData);
    }

    @Override
    public String getTemplateName() {
        return "询问通知书.docx";
    }

    @Override
    public String getDocumentType() {
        return "INQUIRY-NOTICE";
    }

    public static Map<String, String> getReverseFieldMapping() {
        Map<String, String> mapping = new HashMap<>();

        mapping.put("FWZXZDTBSJYFWZXSJS", "service_center_sync_flag");
        mapping.put("DDSJJZ", "arrival_time_end");
        mapping.put("DWJC", "org_short_name");
        mapping.put("XGR", "modifier");
        mapping.put("WSHQ", "full_doc_no");
        mapping.put("XGSJ", "modify_time");
        mapping.put("ZY", "occupation");
        mapping.put("SJSSBMYYTYSJQXCL", "own_dept_uuid");
        mapping.put("XTCJSJCXBYDX", "sys_create_time");
        mapping.put("AY", "case_reason");
        mapping.put("SJBM", "city_org_code");
        mapping.put("FWZXZDTBSJYFWZXSJG", "service_center_sync_update");
        mapping.put("KZZD1", "ext1");
        mapping.put("BAR", "case_handler");
        mapping.put("XWTZSBS", "inquiry_notice_id");
        mapping.put("KZZD3", "ext3");
        mapping.put("GZDW", "work_unit");
        mapping.put("XTGXSJCXBYDX", "sys_modify_time");
        mapping.put("LXDH", "contact_phone");
        mapping.put("ND", "year");
        mapping.put("BZ", "remark");
        mapping.put("AJBS", "case_uuid");
        mapping.put("SFYX", "is_active");
        mapping.put("ZZ", "address");
        mapping.put("KZZD2", "ext2");
        mapping.put("DSR", "party_name");
        mapping.put("WSH", "doc_no");
        mapping.put("CJSJ", "create_time");
        mapping.put("WSRQ", "doc_date");
        mapping.put("WSZT", "doc_status");
        mapping.put("XWDD", "inquiry_location");
        mapping.put("MCRKSJ", "mc_storage_time");
        mapping.put("SFZHM", "id_card_no");
        mapping.put("DWSXZ", "org_abbr");
        mapping.put("NL", "age");
        mapping.put("CJR", "creator");
        mapping.put("SJSSDWYYTYSJQXCL", "own_org_uuid");
        mapping.put("DDSJKS", "arrival_time_start");
        mapping.put("XYWYBS", "industry_unique_id");
        mapping.put("XB", "gender");
        mapping.put("SJMC", "city_org_name");

        return mapping;
    }

    /**
     * 获取模拟数据
     * @param type 数据类型：0-模拟数据(dev环境)，1-真实数据(prod环境)
     * @param caseId 案件ID
     */
    private Map<String, Object> getMockData(int type, String caseId) {

        Map<String, Object> mockData = new HashMap<>();
        if(type == 1) {
            Map<String, Object> query = new HashMap<>();
            query.put("AJBS", caseId);
            JSONArray array = icaseInfoService.getInquiryNoticeDailyReport(query);

            // 如果array不为空，将第一条数据传给mockData
            if(array != null && array.size() > 0) {
                Map<String, Object> firstData = (Map<String, Object>) array.get(0);
                Map<String, Object> processData = new HashMap<>();
                Map<String, String> mapper = getReverseFieldMapping();
                if(firstData != null) {
                    // 处理数据
                    firstData.forEach((key, value) -> {
                        String newKey = mapper.get(key);
                        if (StrUtil.isBlank(newKey)) {
                            newKey = key;
                        }
                        processData.put(newKey, value);
                    });
                    return processData;
                }
            }
        }

        // 基础信息
        mockData.put("inquiry_notice_id", "xwtzs001");
        mockData.put("case_uuid", "24909616d4b042d8bb4b7e693382e9bb");
        mockData.put("org_short_name", "广东省惠州市博罗县烟草专卖局");
        mockData.put("full_doc_no", "博烟询﹝2025﹞第001号");
        mockData.put("doc_no", "001");
        mockData.put("doc_date", "2025/8/28");
        mockData.put("year", "2025");

        // 当事人信息
        mockData.put("party_name", "梁俊强");
        mockData.put("gender", 1); // 1-男性，0-女性
        mockData.put("age", 32);
        mockData.put("occupation", "个体工商户");
        mockData.put("work_unit", "博罗县龙溪隆胜轩茶烟酒商行");
        mockData.put("id_card_no", "******************");
        mockData.put("address", "广东省博罗县龙溪街道长湖村合湖小组193号");
        mockData.put("contact_phone", "13640736270");

        // 案件信息
        mockData.put("case_reason", "未在当地烟草专卖批发企业进货");
        mockData.put("case_handler", "叶辉明,朱兆强");
        mockData.put("inquiry_location", "广东省惠州市博罗县龙溪街道宫庭村龙桥大道1239号");

        // 时间信息
        mockData.put("arrival_time_start", "2025/8/28");
        mockData.put("arrival_time_end", "2025/12/31");

        // 系统字段
        mockData.put("is_active", 1);
        mockData.put("creator", "蔡秋宝");
        mockData.put("create_time", "2025/8/28 10:30");
        mockData.put("modifier", "蔡秋宝");
        mockData.put("modify_time", "2025/8/28 10:30");
        mockData.put("sys_create_time", "2025/8/28 10:30");
        mockData.put("sys_modify_time", "2025/8/28 10:30");
        mockData.put("mc_storage_time", "2025/8/28 10:30");

        // 组织信息
        mockData.put("own_dept_uuid", "4413231030000002829");
        mockData.put("own_org_uuid", "4413231030000000540");
        mockData.put("city_org_code", "10441300");
        mockData.put("city_org_name", "惠州市");
        mockData.put("org_abbr", "博罗");

        // 文书状态
        mockData.put("doc_status", "已生成");

        // 扩展字段
        mockData.put("ext1", "");
        mockData.put("ext2", "");
        mockData.put("ext3", "");
        mockData.put("remark", "");
        mockData.put("industry_unique_id", "tid001");
        mockData.put("service_center_sync_flag", 0);
        mockData.put("service_center_sync_update", 0);

        return mockData;
    }
}
