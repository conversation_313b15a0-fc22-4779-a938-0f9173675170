<template>
  <div class="inquiry-notice-container">
    <!-- 文档头部操作栏 -->
    <div class="document-header">
      <div class="header-left">
        <h3>询问通知书</h3>
      </div>
      <div class="header-right">
        <el-button
          v-if="!isEditing"
          size="small"
          type="primary"
          @click="toggleEdit"
          icon="Edit">
          编辑
        </el-button>
        <template v-else>
          <el-button
            size="small"
            type="success"
            @click="saveEdit"
            icon="Check">
            保存
          </el-button>
          <el-button
            size="small"
            type="info"
            @click="cancelEdit"
            icon="Close">
            取消
          </el-button>
        </template>
        <el-button
          size="small"
          @click="downloadOriginalFile"
          icon="Download">
          导出
        </el-button>
      </div>
    </div>

    <!-- 直接显示DOCX预览，不使用弹窗 -->
    <div class="document-preview" v-if="props.fileUrl&&!isEditing">
      <div class="preview-content" v-loading="previewLoading">
        <VueOfficeDocx
          :src="props.fileUrl"
          style="width: 100%;"
          @rendered="onDocxRendered"
          @error="onPreviewError"
        />
      </div>
    </div>

    <!-- 如果没有文件URL或不是DOCX，显示表单编辑模式 -->
    <div v-else class="document-body">
      <template v-if="isEditing">
        <div class="document-layout">
          <!-- 文档头部 -->
          <div class="document-header-section">
            <div class="org-name">
              <el-input
                v-model="formData.org_short_name"
                placeholder="机构简称"
                class="org-input"
              />
              <span class="org-suffix">烟草专卖局</span>
            </div>

            <div class="document-title">
              <h2>询问通知书</h2>
            </div>

            <div class="document-number">
              <el-input
                v-model="formData.full_doc_no"
                placeholder="文书号全称"
                style="width: 200px;"
              />
            </div>
          </div>

          <!-- 当事人姓名 -->
          <div class="content-section">
            <div class="party-name-section">
              <el-input
                v-model="formData.party_name"
                placeholder="当事人姓名"
                class="party-name-input"
                style="width: 200px;"
              />
              <span>：</span>
            </div>
          </div>

          <!-- 案件信息 -->
          <div class="content-section case-section">
            <span>为调查</span>
            <el-input
              v-model="formData.case_reason"
              placeholder="案件原因"
              class="case-input"
              style="width: 300px; margin: 0 5px;"
            />
            <span>一案，请你（单位）于</span>
          </div>

          <!-- 询问时间 -->
          <div class="content-section time-section">
            <el-input
              v-model="formData.inquiry_year"
              placeholder="年"
              style="width: 80px; margin: 0 2px;"
            />
            <span>年</span>
            <el-input
              v-model="formData.inquiry_month"
              placeholder="月"
              style="width: 60px; margin: 0 2px;"
            />
            <span>月</span>
            <el-input
              v-model="formData.inquiry_day"
              placeholder="日"
              style="width: 60px; margin: 0 2px;"
            />
            <span>日</span>
            <el-input
              v-model="formData.inquiry_hour"
              placeholder="时"
              style="width: 60px; margin: 0 2px;"
            />
            <span>时</span>
            <el-input
              v-model="formData.inquiry_minute"
              placeholder="分"
              style="width: 60px; margin: 0 2px;"
            />
            <span>分到我局（地址：</span>
            <el-input
              v-model="formData.bureau_address"
              placeholder="局地址"
              style="width: 300px; margin: 0 2px;"
            />
            <span>）接受询问。</span>
          </div>

          <!-- 法律依据 -->
          <div class="content-section">
            <p>依据《烟草专卖行政处罚程序规定》第三十八条第二款的规定，你（单位）有如实回答询问、协助调查的义务。</p>
          </div>

          <!-- 携带材料 -->
          <div class="content-section">
            <p><strong>请携带以下材料：</strong></p>
            <div class="material-list">
              <div class="material-item">
                <span>1.</span>
                <el-input
                  v-model="formData.material_1"
                  placeholder="材料1"
                  style="width: 400px; margin-left: 10px;"
                />
              </div>
              <div class="material-item">
                <span>2.</span>
                <el-input
                  v-model="formData.material_2"
                  placeholder="材料2"
                  style="width: 400px; margin-left: 10px;"
                />
              </div>
              <div class="material-item">
                <span>3.</span>
                <el-input
                  v-model="formData.material_3"
                  placeholder="材料3"
                  style="width: 400px; margin-left: 10px;"
                />
              </div>
            </div>
          </div>

          <!-- 委托说明 -->
          <div class="content-section">
            <p>如你（单位）委托其他人员接受询问调查的，委托代理人应当同时提供授权委托书及委托代理人身份证明。</p>
          </div>

          <!-- 联系人信息 -->
          <div class="content-section contact-section">
            <div class="contact-item">
              <span>联系人：</span>
              <el-input
                v-model="formData.contact_person"
                placeholder="联系人"
                style="width: 150px; margin-left: 10px;"
              />
            </div>
            <div class="contact-item">
              <span>联系电话：</span>
              <el-input
                v-model="formData.contact_phone_bureau"
                placeholder="联系电话"
                style="width: 150px; margin-left: 10px;"
              />
            </div>
          </div>

          <!-- 落款区域 -->
          <div class="signature-section">
            <div class="seal-section">
              <el-input
                v-model="formData.seal_org"
                placeholder="落款单位"
                style="width: 300px;"
              />
            </div>
            <div class="date-line">
              <el-input
                v-model="formData.seal_year"
                placeholder="年"
                style="width: 80px; margin: 0 2px;"
              />
              <span>年</span>
              <el-input
                v-model="formData.seal_month"
                placeholder="月"
                style="width: 60px; margin: 0 2px;"
              />
              <span>月</span>
              <el-input
                v-model="formData.seal_day"
                placeholder="日"
                style="width: 60px; margin: 0 2px;"
              />
              <span>日</span>
            </div>
          </div>

          <!-- 承办人信息 -->
          <div class="handler-section">
            <div class="handler-item">
              <span>承办人（签名）：</span>
              <el-input
                v-model="formData.handler_1_name"
                placeholder="承办人1"
                style="width: 100px; margin: 0 5px;"
              />
              <span>执法证号：</span>
              <el-input
                v-model="formData.handler_1_cert_no"
                placeholder="执法证号"
                style="width: 120px; margin: 0 5px;"
              />
              <el-input
                v-model="formData.handler_1_sign_year"
                placeholder="年"
                style="width: 60px; margin: 0 2px;"
              />
              <span>年</span>
              <el-input
                v-model="formData.handler_1_sign_month"
                placeholder="月"
                style="width: 50px; margin: 0 2px;"
              />
              <span>月</span>
              <el-input
                v-model="formData.handler_1_sign_day"
                placeholder="日"
                style="width: 50px; margin: 0 2px;"
              />
              <span>日</span>
            </div>
            <div class="handler-item">
              <span style="margin-left: 120px;"></span>
              <el-input
                v-model="formData.handler_2_name"
                placeholder="承办人2"
                style="width: 100px; margin: 0 5px;"
              />
              <span>执法证号：</span>
              <el-input
                v-model="formData.handler_2_cert_no"
                placeholder="执法证号"
                style="width: 120px; margin: 0 5px;"
              />
              <el-input
                v-model="formData.handler_2_sign_year"
                placeholder="年"
                style="width: 60px; margin: 0 2px;"
              />
              <span>年</span>
              <el-input
                v-model="formData.handler_2_sign_month"
                placeholder="月"
                style="width: 50px; margin: 0 2px;"
              />
              <span>月</span>
              <el-input
                v-model="formData.handler_2_sign_day"
                placeholder="日"
                style="width: 50px; margin: 0 2px;"
              />
              <span>日</span>
            </div>
          </div>

          <!-- 当事人意见及送达信息 -->
          <div class="delivery-section">
            <div class="delivery-item">
              <span>当事人意见及签名：</span>
              <el-input
                v-model="formData.party_opinion"
                placeholder="当事人意见"
                style="width: 300px; margin-left: 10px;"
              />
            </div>
            <div class="delivery-item">
              <span>送达方式：</span>
              <el-input
                v-model="formData.delivery_method"
                placeholder="送达方式"
                style="width: 150px; margin: 0 10px;"
              />
              <span>送达地点：</span>
              <el-input
                v-model="formData.delivery_location"
                placeholder="送达地点"
                style="width: 200px; margin-left: 10px;"
              />
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Check, View, Download, FullScreen, ScaleToOriginal, Close } from '@element-plus/icons-vue'
import VueOfficeDocx from '@vue-office/docx'
// 在组件中导入样式
import '@/styles/vue-office-docx.css'
import '@/styles/document-common.scss';

const props = defineProps({
  documentData: {
    type: Object,
    default: () => ({})
  },
  fileUrl: {
    type: String,
    default: ''
  },
  fileType: {
    type: String,
    default: ''
  },
  fileName: {
    type: String,
    default: ''
  }
})

// 添加调试日志
watch(() => props.fileUrl, (newVal, oldVal) => {
  console.log('fileUrl changed:', { newVal, oldVal })
}, { immediate: true })

watch(() => props, (newVal) => {
  console.log('所有props:', newVal)
}, { immediate: true, deep: true })

const emit = defineEmits(['save'])

// 编辑状态
const isEditing = ref(false)

// 表单数据
const formData = ref({
  org_short_name: '',
  full_doc_no: '',
  party_name: '',
  case_reason: '',
  inquiry_year: '',
  inquiry_month: '',
  inquiry_day: '',
  inquiry_hour: '',
  inquiry_minute: '',
  bureau_address: '',
  material_1: '',
  material_2: '',
  material_3: '',
  contact_person: '',
  contact_phone_bureau: '',
  seal_org: '',
  seal_year: '',
  seal_month: '',
  seal_day: '',
  handler_1_name: '',
  handler_1_cert_no: '',
  handler_1_sign_year: '',
  handler_1_sign_month: '',
  handler_1_sign_day: '',
  handler_2_name: '',
  handler_2_cert_no: '',
  handler_2_sign_year: '',
  handler_2_sign_month: '',
  handler_2_sign_day: '',
  party_opinion: '',
  delivery_method: '',
  delivery_location: ''
})

// 预览相关状态
const previewDialogVisible = ref(false)
const previewLoading = ref(false)
const isFullscreen = ref(false)

// 保存原始数据的备份
const originalFormData = ref({})

// 监听传入的文档数据
watch(() => props.documentData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 从documentContent中提取属性，先解析JSON字符串
    let docContent = {}
    try {
      if (typeof newVal.documentContent === 'string') {
        docContent = JSON.parse(newVal.documentContent)
      } else {
        docContent = newVal.documentContent || {}
      }
    } catch (error) {
      docContent = {}
    }

    // 合并数据，优先使用documentContent中的数据
    formData.value = {
      ...formData.value,
      ...newVal,
      // 从documentContent中提取具体字段
      org_short_name: docContent.org_short_name || newVal.org_short_name || '',
      full_doc_no: docContent.full_doc_no || newVal.full_doc_no || '',
      party_name: docContent.party_name || newVal.party_name || '',
      case_reason: docContent.case_reason || newVal.case_reason || '',
      inquiry_year: docContent.inquiry_year || newVal.inquiry_year || '',
      inquiry_month: docContent.inquiry_month || newVal.inquiry_month || '',
      inquiry_day: docContent.inquiry_day || newVal.inquiry_day || '',
      inquiry_hour: docContent.inquiry_hour || newVal.inquiry_hour || '',
      inquiry_minute: docContent.inquiry_minute || newVal.inquiry_minute || '',
      bureau_address: docContent.bureau_address || newVal.bureau_address || '',
      material_1: docContent.material_1 || newVal.material_1 || '',
      material_2: docContent.material_2 || newVal.material_2 || '',
      material_3: docContent.material_3 || newVal.material_3 || '',
      contact_person: docContent.contact_person || newVal.contact_person || '',
      contact_phone_bureau: docContent.contact_phone_bureau || newVal.contact_phone_bureau || '',
      seal_org: docContent.seal_org || newVal.seal_org || '',
      seal_year: docContent.seal_year || newVal.seal_year || '',
      seal_month: docContent.seal_month || newVal.seal_month || '',
      seal_day: docContent.seal_day || newVal.seal_day || '',
      handler_1_name: docContent.handler_1_name || newVal.handler_1_name || '',
      handler_1_cert_no: docContent.handler_1_cert_no || newVal.handler_1_cert_no || '',
      handler_2_name: docContent.handler_2_name || newVal.handler_2_name || '',
      handler_2_cert_no: docContent.handler_2_cert_no || newVal.handler_2_cert_no || '',
      party_opinion: docContent.party_opinion || newVal.party_opinion || '',
      delivery_method: docContent.delivery_method || newVal.delivery_method || '',
      delivery_location: docContent.delivery_location || newVal.delivery_location || ''
    }

    // 保存原始数据的备份
    originalFormData.value = { ...formData.value }
  }
}, { immediate: true, deep: true })

// 修改切换编辑状态函数
const toggleEdit = () => {
  if (!isEditing.value) {
    // 进入编辑模式时保存当前数据作为备份
    originalFormData.value = { ...formData.value }
  }
  isEditing.value = !isEditing.value
}

// 新增保存编辑函数
const saveEdit = () => {
  // 保存时只传递需要的数据字段，避免传递整个formData
  const saveData = {
    org_short_name: formData.value.org_short_name,
    full_doc_no: formData.value.full_doc_no,
    party_name: formData.value.party_name,
    case_reason: formData.value.case_reason,
    inquiry_year: formData.value.inquiry_year,
    inquiry_month: formData.value.inquiry_month,
    inquiry_day: formData.value.inquiry_day,
    inquiry_hour: formData.value.inquiry_hour,
    inquiry_minute: formData.value.inquiry_minute,
    bureau_address: formData.value.bureau_address,
    material_1: formData.value.material_1,
    material_2: formData.value.material_2,
    material_3: formData.value.material_3,
    contact_person: formData.value.contact_person,
    contact_phone_bureau: formData.value.contact_phone_bureau,
    seal_org: formData.value.seal_org,
    seal_year: formData.value.seal_year,
    seal_month: formData.value.seal_month,
    seal_day: formData.value.seal_day,
    handler_1_name: formData.value.handler_1_name,
    handler_1_cert_no: formData.value.handler_1_cert_no,
    handler_2_name: formData.value.handler_2_name,
    handler_2_cert_no: formData.value.handler_2_cert_no,
    party_opinion: formData.value.party_opinion,
    delivery_method: formData.value.delivery_method,
    delivery_location: formData.value.delivery_location
  }

  emit('save', saveData)
  isEditing.value = false
}

// 新增取消编辑函数
const cancelEdit = () => {
  // 恢复原始数据
  formData.value = { ...originalFormData.value }
  isEditing.value = false
  ElMessage.info('已取消编辑，数据已恢复')
}

// 文档渲染完成回调
const onDocxRendered = () => {
  previewLoading.value = false
}

// 预览错误处理
const onPreviewError = (error) => {
  previewLoading.value = false
  console.error('文档预览失败:', error)
  ElMessage.error('文档预览失败，请检查文件格式或网络连接')
}

// 下载原文件
const downloadOriginalFile = () => {
  if (props.fileUrl) {
    const link = document.createElement('a')
    link.href = props.fileUrl
    link.download = props.fileName || '询问通知书'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('开始下载原文件')
  } else {
    ElMessage.warning('原文件下载链接不存在')
  }
}

// 在 onMounted 中添加事件监听
onMounted(() => {
  // 监听优化事件
  const handleOptimizeEvent = (event) => {
    const { action } = event.detail
    // 根据不同的优化动作进入编辑模式
    if (action === 'partyInfo' || action === 'inquiryTime' || action === 'materials') {
      isEditing.value = true
      ElMessage.info('已进入编辑模式，请完善相关信息')
    }
  }

  document.addEventListener('document-optimize', handleOptimizeEvent)

  // 组件卸载时移除监听器
  onUnmounted(() => {
    document.removeEventListener('document-optimize', handleOptimizeEvent)
  })
})
</script>

<style scoped>
/* 组件特有样式已移至 @/styles/document-common.scss */
.material-list {
  margin: 10px 0;
}

.material-item {
  display: flex;
  align-items: center;
  margin: 5px 0;
}

.contact-section {
  margin: 20px 0;
}

.contact-item {
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.handler-section {
  margin: 20px 0;
}

.handler-item {
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.delivery-section {
  margin: 20px 0;
}

.delivery-item {
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.org-suffix {
  margin-left: 5px;
}

.party-name-section {
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.time-section {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin: 10px 0;
}

.seal-section {
  text-align: center;
  margin: 20px 0;
}
</style>
